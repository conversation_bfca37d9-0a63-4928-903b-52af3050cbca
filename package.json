{"name": "cf-clearance-scraper", "version": "2.1.3", "main": "index.js", "scripts": {"start": "node start.js", "start:auto": "node start.js", "start:dev": "node start.js --browserLimit=10", "start:prod": "node start.js --browserLimit=25", "start:light": "node start.js --browserLimit=5", "start:manual": "node start.js --maxMemoryUsage=4096 --maxConcurrentRequests=60 --contextPoolSize=20", "test": "node --experimental-vm-modules ./node_modules/.bin/jest --detectOpenHandles --verbose", "test:quick": "node tests/quick_test.js", "test:cf5s": "node tests/test_cf5s.js", "test:cfcookie": "node tests/test_cfcookie.js", "check-chrome": "node scripts/check_chrome.js", "diagnose": "node scripts/check_chrome.js"}, "author": "zfcsoftware", "license": "ISC", "description": "本地版本的 Cloudflare 保护绕过工具，支持页面源码获取、Turnstile令牌生成和WAF会话创建。无需Docker，直接本地部署。", "dependencies": {"ajv": "^8.17.1", "ajv-formats": "^3.0.1", "axios": "^1.10.0", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.0", "jest": "^29.7.0", "puppeteer-real-browser": "^1.4.0", "supertest": "^7.0.0"}}