<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地打码服务监控</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>本地打码服务监控</h1>
        </div>

        <div class="dashboard">
            <!-- 可用服务卡片 -->
            <div class="card">
                <h3><span class="icon">🛠️</span>可用服务</h3>
                <div id="serviceStatus" class="loading">加载中...</div>
            </div>

            <!-- 实例信息卡片 -->
            <div class="card">
                <h3><span class="icon">🖥️</span>实例信息</h3>
                <div id="instanceInfo" class="loading">加载中...</div>
            </div>

            <!-- 请求统计卡片 -->
            <div class="card">
                <h3><span class="icon">📊</span>请求统计</h3>
                <div id="requestStats" class="loading">加载中...</div>
            </div>

            <!-- 系统资源卡片 -->
            <div class="card">
                <h3><span class="icon">⚙️</span>系统资源</h3>
                <div id="systemResources" class="loading">加载中...</div>
            </div>

            <!-- 负载历史图表 -->
            <div class="card load-history-card" style="grid-column: 1 / -1;">
                <h3>
                    <span class="icon">📊</span>负载历史
                    <span class="live-indicator">实时 1s</span>
                </h3>
                <div class="metrics-summary" id="metricsSummary" style="display: flex; gap: 20px; margin-bottom: 10px; flex-wrap: wrap;">
                    <div class="metric-item">
                        <span class="metric-label">当前活跃</span>
                        <span class="metric-value" id="currentActive">0</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">峰值并发</span>
                        <span class="metric-value" id="peakConcurrent">0</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">平均负载</span>
                        <span class="metric-value" id="avgLoad">0.0</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">总容量</span>
                        <span class="metric-value" id="totalCapacity">100</span>
                    </div>
                </div>
                <div class="chart-container load-chart-container">
                    <canvas id="loadHistoryChart" class="chart-canvas"></canvas>
                </div>
            </div>
        </div>

        <!-- 请求历史与实时状态 -->
        <div class="table-container">
            <h3>
                <span class="icon">📋</span>请求记录与实时状态 
                <button class="btn" onclick="resetMonitorData()">重置数据</button>
                <button class="btn" onclick="restartService()">重启服务</button>
                <span class="live-indicator">实时更新</span>
            </h3>
            <div id="unifiedRequestTable" class="loading">加载中...</div>
        </div>

        <div class="footer">
            <div style="display: flex; justify-content: center; align-items: center; gap: 30px;">
                <span>
                    Designed by <a href="https://x.com/Johnze268" target="_blank">
                        <img src="https://abs.twimg.com/favicons/twitter.2.ico" alt="Twitter"> 0xsongsu
                    </a>
                </span>
                <span>
                    Powered by <a href="https://claude.ai" target="_blank">
                        <img src="https://claude.ai/favicon.ico" alt="Claude">
                    </a>
                </span>
            </div>
        </div>
    </div>

    <script src="charts.js"></script>
    <script>
        let updateInterval;
        let loadChart = null;
        let loadHistory = [];
        let peakConcurrent = 0;
        
        // 初始化图表
        function initCharts() {
            const canvas = document.getElementById('loadHistoryChart');
            if (canvas) {
                loadChart = new LoadHistoryChart(canvas, {
                    backgroundColor: '#f8f9fa',
                    fillColor: 'rgba(52, 144, 220, 0.2)',
                    lineColor: 'rgba(52, 144, 220, 1)',
                    lineWidth: 2,
                    maxDataPoints: 60
                });
            }
        }

        // 格式化时间
        function formatTime(timestamp) {
            return new Date(timestamp).toLocaleString('zh-CN');
        }

        // 格式化持续时间
        function formatDuration(ms) {
            if (ms < 1000) return `${ms}ms`;
            if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
            if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
            return `${(ms / 3600000).toFixed(1)}h`;
        }

        // 格式化内存大小
        function formatMemory(mb) {
            if (mb < 1024) return `${mb}MB`;
            return `${(mb / 1024).toFixed(1)}GB`;
        }

        // 获取监控数据
        async function fetchMonitorData() {
            try {
                let response = await fetch('/api/monitor');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                updateDashboard(data);
                
            } catch (error) {
                console.error('获取监控数据失败:', error);
                showError('获取监控数据失败: ' + error.message);
            }
        }

        // 显示错误信息
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            
            // 在每个loading元素中显示错误
            document.querySelectorAll('.loading').forEach(element => {
                element.innerHTML = '';
                element.appendChild(errorDiv.cloneNode(true));
            });
        }

        // 更新仪表板
        function updateDashboard(data) {
            updateServiceStatus(data);
            updateInstanceInfo(data);
            updateRequestStats(data);
            updateSystemResources(data);
            updateUnifiedRequestTable(data);
            updateLoadMetrics(data);
            updateCharts(data);
        }

        // 更新服务状态
        function updateServiceStatus(data) {
            const element = document.getElementById('serviceStatus');
            
            element.innerHTML = `
                <div style="display: flex; justify-content: center;">
                    <!-- 只保留 Cloudflare Turnstile -->
                    <div style="padding: 20px; background: rgba(28, 168, 221, 0.1); border-radius: 12px; text-align: center; border: 2px solid rgba(28, 168, 221, 0.2); min-width: 200px;">
                        <div style="margin-bottom: 15px;">
                            <img src="https://dash.cloudflare.com/favicon.ico" alt="Cloudflare" style="width: 40px; height: 40px;">
                        </div>
                        <div style="font-weight: bold; color: #1ca8dd; margin-bottom: 8px; font-size: 1.1rem;">Cloudflare</div>
                        <div style="color: #27ae60; font-size: 0.9rem; font-weight: 600;">
                            <span class="status-indicator status-running" style="margin-right: 6px;"></span>
                            运行中
                        </div>
                        <div style="color: #7f8c8d; font-size: 0.8rem; margin-top: 5px;">Turnstile 解决</div>
                    </div>
                </div>
            `;
        }

        // 更新实例信息
        function updateInstanceInfo(data) {
            const element = document.getElementById('instanceInfo');
            const usagePercent = data.instances.total > 0 ? 
                (data.instances.active / data.instances.total * 100).toFixed(1) : 0;
            
            let instanceDetailsHtml = '';
            
            // 显示各个容器的详细信息（如果有多容器部署）
            if (data.containers && data.containers.length > 0) {
                instanceDetailsHtml = `
                    <div style="margin-top: 15px; border-top: 1px solid rgba(0,0,0,0.1); padding-top: 15px;">
                        <h4 style="margin: 0 0 10px 0; font-size: 0.9rem; color: #34495e;">容器详情</h4>
                        ${data.containers.map(container => `
                            <div style="margin-bottom: 8px; padding: 8px; background: rgba(255,255,255,0.5); border-radius: 4px; font-size: 0.8rem;">
                                <strong>${container.host}</strong>
                                <span style="float: right;">
                                    <span class="status-indicator status-running"></span>
                                    ${container.instances.total}实例 (${container.instances.active}活跃/${container.instances.available}空闲)
                                </span>
                            </div>
                        `).join('')}
                    </div>
                `;
            }
            
            // 显示浏览器实例池详情
            let browserPoolHtml = '';
            if (data.browserPoolDetails && data.browserPoolDetails.length > 0) {
                browserPoolHtml = `
                    <div style="margin-top: 15px; border-top: 1px solid rgba(0,0,0,0.1); padding-top: 15px;">
                        <h4 style="margin: 0 0 10px 0; font-size: 0.9rem; color: #34495e;">浏览器实例池</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 8px;">
                            ${data.browserPoolDetails.map(instance => `
                                <div style="padding: 8px; background: rgba(255,255,255,0.5); border-radius: 4px; text-align: center; font-size: 0.75rem;">
                                    <div style="font-weight: bold;">实例 #${instance.id}</div>
                                    <div style="margin: 2px 0;">
                                        <span class="status-indicator ${instance.status === 'idle' ? 'status-running' : 'status-warning'}"></span>
                                        ${instance.status === 'idle' ? '空闲' : '忙碌'}
                                    </div>
                                    <div style="color: #7f8c8d; font-size: 0.7rem;">
                                        上下文: ${instance.contexts || 0}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }
            
            element.innerHTML = `
                <div class="stat-grid" style="grid-template-columns: 1fr 1fr;">
                    <div class="stat-item">
                        <div class="stat-value">${data.instances.active}</div>
                        <div class="stat-label">忙碌实例</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.instances.available}</div>
                        <div class="stat-label">空闲实例</div>
                    </div>
                </div>
                <div style="margin-top: 15px; border-top: 1px solid rgba(0,0,0,0.1); padding-top: 15px;">
                    <h4 style="margin: 0 0 10px 0; font-size: 0.9rem; color: #34495e;">服务实例分布</h4>
                    <div style="display: flex; justify-content: center;">
                        <div style="padding: 12px; background: rgba(28, 168, 221, 0.1); border-radius: 8px; text-align: center; font-size: 0.85rem; min-width: 150px;">
                            <div style="font-weight: bold; color: #1ca8dd; margin-bottom: 5px;">Cloudflare</div>
                            <div style="color: #7f8c8d; margin-bottom: 2px;">活跃实例: ${data.instances.active}</div>
                            <div style="color: #7f8c8d; font-size: 0.75rem;">Turnstile</div>
                        </div>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${usagePercent}%"></div>
                </div>
                <div style="text-align: center; margin-top: 5px; font-size: 0.9rem; color: #7f8c8d;">
                    使用率: ${usagePercent}%
                </div>
                ${instanceDetailsHtml}
                ${browserPoolHtml}
            `;
        }

        // 更新请求统计
        function updateRequestStats(data) {
            const element = document.getElementById('requestStats');
            
            // 更新历史数据
            const now = new Date();
            historyData.activeRequests.push(data.requests.active);
            historyData.successRate.push(data.requests.successRate);
            
            // 保持数据点数量不超过最大值
            if (historyData.activeRequests.length > maxHistoryPoints) {
                historyData.activeRequests.shift();
                historyData.successRate.shift();
            }
            
            element.innerHTML = `
                <!-- 颜色图例 -->
                <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 8px;">
                    <div style="display: flex; align-items: center; gap: 5px;">
                        <div style="width: 16px; height: 2px; background: #28a745;"></div>
                        <span style="color: #28a745; font-size: 0.8rem;">活跃请求</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 5px;">
                        <div style="width: 16px; height: 2px; background: #ff6b6b;"></div>
                        <span style="color: #ff6b6b; font-size: 0.8rem;">成功率</span>
                    </div>
                </div>
                
                <!-- 请求统计图表 -->
                <div style="background: #1a1a1a; border-radius: 8px; padding: 12px; height: 180px; position: relative;">
                    <!-- 统计信息（右上角） -->
                    <div style="position: absolute; top: 12px; right: 12px; text-align: right; font-size: 0.7rem; color: #ffffff; line-height: 1.2; z-index: 10;">
                        <div style="color: #00ff88;">总: ${data.requests.total}</div>
                        <div style="color: #00ddff;">成功: ${data.requests.successful}</div>
                        <div style="color: #ff8888;">失败: ${data.requests.failed}</div>
                    </div>
                    
                    <!-- 图表画布（背景） -->
                    <canvas id="requestChart" width="100" height="156" style="position: absolute; top: 12px; left: 12px; width: calc(100% - 24px); height: calc(100% - 24px); z-index: 1;"></canvas>
                    
                    <!-- 左侧刻度（活跃请求） -->
                    <div style="position: absolute; left: 8px; top: 50%; transform: translateY(-50%); color: #28a745; z-index: 10; font-size: 0.9rem; font-weight: bold;">${data.requests.active}</div>
                    
                    <!-- 右侧刻度（成功率） -->
                    <div style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); color: #ff6b6b; text-align: right; z-index: 10; font-size: 0.9rem; font-weight: bold;">${data.requests.successRate}%</div>
                </div>
            `;
            
            // 绘制双折线图表
            setTimeout(() => {
                drawDualLineChart('requestChart', historyData.activeRequests, historyData.successRate, '#28a745', '#ff6b6b');
            }, 100);
        }

        // 历史数据存储
        const historyData = {
            cpu: [],
            memory: [],
            activeRequests: [],
            successRate: [],
            timestamps: []
        };
        
        const maxHistoryPoints = 50; // 保留最近50个数据点

        // 更新系统资源信息
        function updateSystemResources(data) {
            const element = document.getElementById('systemResources');
            const memory = data.memory;
            
            // 计算使用率
            const cpuUsage = data.memory.cpu?.current || 0;
            const memoryUsage = (data.memory.system.used / data.memory.system.total) * 100;
            
            // 更新历史数据
            const now = new Date();
            historyData.cpu.push(cpuUsage);
            historyData.memory.push(memoryUsage);
            historyData.timestamps.push(now);
            
            // 保持数据点数量不超过最大值
            if (historyData.cpu.length > maxHistoryPoints) {
                historyData.cpu.shift();
                historyData.memory.shift();
                historyData.timestamps.shift();
            }
            
            element.innerHTML = `
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    <!-- CPU 使用率图表 -->
                    <div style="background: #1a1a1a; border-radius: 8px; padding: 12px; height: 120px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <span style="color: #00ffff; font-size: 0.85rem; font-weight: 500;">CPU 使用率</span>
                            <span style="color: #00ffff; font-size: 0.9rem; font-weight: bold;">${cpuUsage.toFixed(1)}%</span>
                        </div>
                        <canvas id="cpuChart" width="100" height="80" style="width: 100%; height: 80px;"></canvas>
                    </div>
                    
                    <!-- 系统内存负载图表 -->
                    <div style="background: #1a1a1a; border-radius: 8px; padding: 12px; height: 120px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <span style="color: #00ffaa; font-size: 0.85rem; font-weight: 500;">系统内存负载</span>
                            <span style="color: #00ffaa; font-size: 0.9rem; font-weight: bold;">${memoryUsage.toFixed(1)}%</span>
                        </div>
                        <canvas id="memoryChart" width="100" height="80" style="width: 100%; height: 80px;"></canvas>
                    </div>
                </div>
            `;
            
            // 绘制图表
            setTimeout(() => {
                drawChart('cpuChart', historyData.cpu, '#00ffff');
                drawChart('memoryChart', historyData.memory, '#00ffaa');
            }, 100);
        }

        // 绘制折线图函数
        function drawChart(canvasId, data, color) {
            const canvas = document.getElementById(canvasId);
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            
            if (data.length < 2) return;
            
            // 设置画布大小适应容器 - 支持高DPI
            const rect = canvas.getBoundingClientRect();
            const dpr = window.devicePixelRatio || 1;
            
            canvas.width = rect.width * dpr;
            canvas.height = rect.height * dpr;
            canvas.style.width = rect.width + 'px';
            canvas.style.height = rect.height + 'px';
            
            ctx.scale(dpr, dpr);
            
            const w = rect.width;
            const h = rect.height;
            
            // 计算数据点位置
            const padding = 10;
            const chartWidth = w - padding * 2;
            const chartHeight = h - padding * 2;
            
            // Y轴范围：1% 到 100%
            const minY = 1;
            const maxY = 100;
            
            // 创建路径
            ctx.beginPath();
            
            // 绘制填充区域
            const gradient = ctx.createLinearGradient(0, padding, 0, h - padding);
            gradient.addColorStop(0, color + '40'); // 40% 透明度
            gradient.addColorStop(1, color + '10'); // 10% 透明度
            
            // 绘制数据点和线条
            for (let i = 0; i < data.length; i++) {
                const x = padding + (i / (data.length - 1)) * chartWidth;
                const y = padding + (1 - (Math.max(data[i], minY) - minY) / (maxY - minY)) * chartHeight;
                
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    // 使用贝塞尔曲线创建平滑线条
                    const prevX = padding + ((i - 1) / (data.length - 1)) * chartWidth;
                    const prevY = padding + (1 - (Math.max(data[i - 1], minY) - minY) / (maxY - minY)) * chartHeight;
                    
                    const cpx = prevX + (x - prevX) * 0.5;
                    const cpy = prevY;
                    const cpx2 = prevX + (x - prevX) * 0.5;
                    const cpy2 = y;
                    
                    ctx.bezierCurveTo(cpx, cpy, cpx2, cpy2, x, y);
                }
            }
            
            // 绘制填充区域
            const fillPath = new Path2D();
            fillPath.moveTo(padding, h - padding); // 底部左边
            
            for (let i = 0; i < data.length; i++) {
                const x = padding + (i / (data.length - 1)) * chartWidth;
                const y = padding + (1 - (Math.max(data[i], minY) - minY) / (maxY - minY)) * chartHeight;
                
                if (i === 0) {
                    fillPath.lineTo(x, y);
                } else {
                    const prevX = padding + ((i - 1) / (data.length - 1)) * chartWidth;
                    const prevY = padding + (1 - (Math.max(data[i - 1], minY) - minY) / (maxY - minY)) * chartHeight;
                    
                    const cpx = prevX + (x - prevX) * 0.5;
                    const cpy = prevY;
                    const cpx2 = prevX + (x - prevX) * 0.5;
                    const cpy2 = y;
                    
                    fillPath.bezierCurveTo(cpx, cpy, cpx2, cpy2, x, y);
                }
            }
            
            fillPath.lineTo(padding + chartWidth, h - padding); // 底部右边
            fillPath.closePath();
            
            // 填充区域
            ctx.fillStyle = gradient;
            ctx.fill(fillPath);
            
            // 绘制线条
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            ctx.stroke();
        }

        // 截断token显示
        function truncateToken(token, maxLength = 30) {
            if (!token || token.length <= maxLength) return token || '';
            return token.substring(0, maxLength) + '...';
        }

        // 复制到剪贴板
        function copyToClipboard(text, element) {
            navigator.clipboard.writeText(text).then(() => {
                const originalText = element.textContent;
                element.textContent = '已复制!';
                element.style.background = '#d4edda';
                element.style.borderColor = '#c3e6cb';
                element.style.color = '#155724';

                setTimeout(() => {
                    element.textContent = originalText;
                    element.style.background = '#f8f9fa';
                    element.style.borderColor = '#dee2e6';
                    element.style.color = '';
                }, 1000);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            });
        }

        // 更新统一请求表格
        function updateUnifiedRequestTable(data) {
            const element = document.getElementById('unifiedRequestTable');
            const allRequests = [];
            
            // 添加活跃请求（处理中状态）
            data.activeRequests.forEach(req => {
                allRequests.push({
                    timestamp: req.startTime,
                    url: req.url,
                    mode: req.mode,
                    status: 'processing',
                    token: '',
                    responseTime: Date.now() - new Date(req.startTime).getTime(),
                    isActive: true,
                    requestId: req.id,
                    clientIP: req.clientIP
                });
            });
            
            // 添加请求历史（已完成/失败状态）
            data.requestHistory.forEach(req => {
                // 查找对应的token - 只根据requestId匹配，避免重复
                const matchingToken = data.recentTokens.find(token =>
                    token.requestId === req.requestId
                );

                allRequests.push({
                    timestamp: req.timestamp,
                    url: req.url,
                    mode: req.mode,
                    status: req.success ? 'completed' : 'failed',
                    token: matchingToken ? matchingToken.token : '',
                    responseTime: req.responseTime,
                    isActive: false,
                    requestId: req.requestId
                });
            });
            
            // 按时间倒序排列
            allRequests.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
            
            if (allRequests.length === 0) {
                element.innerHTML = '<p style="text-align: center; color: #7f8c8d; padding: 20px;">暂无请求记录</p>';
                return;
            }
            
            const tableHTML = `
                <table>
                    <thead>
                        <tr>
                            <th style="width: 120px;">时间</th>
                            <th style="width: 120px; max-width: 120px;">URL</th>
                            <th style="width: 100px;">服务类型</th>
                            <th style="width: 100px;">状态</th>
                            <th style="width: 200px;">Token/Cookie</th>
                            <th style="width: 90px;">响应时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${allRequests.slice(0, 50).map(req => {
                            let statusDisplay = '';
                            let statusClass = '';
                            
                            switch(req.status) {
                                case 'processing':
                                    statusDisplay = '处理中';
                                    statusClass = 'status-warning';
                                    break;
                                case 'completed':
                                    statusDisplay = '已完成';
                                    statusClass = 'status-running';
                                    break;
                                case 'failed':
                                    statusDisplay = '失败';
                                    statusClass = 'status-error';
                                    break;
                            }
                            
                            const truncatedToken = truncateToken(req.token, 30);
                            const responseTimeDisplay = req.status === 'processing'
                                ? `${formatDuration(req.responseTime)} ⏱️`
                                : formatDuration(req.responseTime);

                            // 根据mode确定服务类型
                            let serviceType = '';
                            let serviceColor = '';

                            if (req.mode === 'turnstile-min' || req.mode === 'turnstile-max') {
                                serviceType = 'CF Token';
                                serviceColor = '#1ca8dd';
                            } else if (req.mode === 'cfcookie') {
                                serviceType = 'CF Cookie';
                                serviceColor = '#e67e22';
                            } else {
                                serviceType = 'Cloudflare';
                                serviceColor = '#1ca8dd';
                            }
                            
                            return `
                                <tr class="${req.isActive ? 'active-request' : ''}" style="${req.isActive ? 'background-color: rgba(255, 193, 7, 0.1);' : ''}">
                                    <td style="font-size: 0.85rem;">${formatTime(req.timestamp).replace(/:\d{2}$/, '')}</td>
                                    <td style="max-width: 120px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${req.url}">
                                        ${req.url}
                                    </td>
                                    <td style="text-align: center;">
                                        <span style="color: ${serviceColor}; font-weight: bold; font-size: 0.8rem;">${serviceType}</span>
                                    </td>
                                    <td>
                                        <span class="status-badge ${statusClass}">${statusDisplay}</span>
                                    </td>
                                    <td style="font-family: monospace; font-size: 0.75rem;" title="${req.token}">
                                        ${truncatedToken ? `<span style="background: #f8f9fa; padding: 2px 4px; border-radius: 3px; border: 1px solid #dee2e6; cursor: pointer;" onclick="copyToClipboard('${req.token.replace(/'/g, "\\'")}', this)">${truncatedToken}</span>` : '-'}
                                    </td>
                                    <td style="font-weight: ${req.isActive ? 'bold' : 'normal'};">
                                        ${responseTimeDisplay}
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
                <div style="margin-top: 10px; font-size: 0.85rem; color: #6c757d; text-align: center;">
                    显示最近 ${Math.min(allRequests.length, 50)} 条记录
                    ${allRequests.length > 50 ? `（共 ${allRequests.length} 条）` : ''}
                </div>
            `;
            
            element.innerHTML = tableHTML;
        }

        // 重置监控数据
        async function resetMonitorData() {
            if (!confirm('确定要重置所有监控数据吗？')) return;
            
            try {
                const response = await fetch('/api/monitor/reset', { method: 'POST' });
                if (response.ok) {
                    // 重置本地数据
                    loadHistory = [];
                    peakConcurrent = 0;
                    if (loadChart) {
                        loadChart.clear();
                    }
                    alert('监控数据已重置');
                    fetchMonitorData();
                } else {
                    throw new Error('重置失败');
                }
            } catch (error) {
                alert('重置失败: ' + error.message);
            }
        }

        // 重启服务
        async function restartService() {
            if (!confirm('确定要重启服务吗？这将清理所有浏览器缓存和实例，可能需要1-2分钟完成。')) return;
            
            try {
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '重启中...';
                button.disabled = true;
                
                const response = await fetch('/api/service/restart', { 
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    alert('服务重启成功！已清理浏览器缓存和实例。');
                    // 等待5秒后重新开始监控
                    setTimeout(() => {
                        fetchMonitorData();
                    }, 5000);
                } else {
                    const error = await response.text();
                    throw new Error(error);
                }
            } catch (error) {
                alert('重启失败: ' + error.message);
            } finally {
                // 恢复按钮状态
                setTimeout(() => {
                    const button = document.querySelector('button[onclick="restartService()"]');
                    if (button) {
                        button.textContent = '重启服务';
                        button.disabled = false;
                    }
                }, 3000);
            }
        }

        // 启动监控
        function startMonitoring() {
            fetchMonitorData(); // 立即获取一次数据
            updateInterval = setInterval(fetchMonitorData, 1000); // 每1秒更新一次
        }

        // 停止监控
        function stopMonitoring() {
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
            }
        }

        // 更新负载指标
        function updateLoadMetrics(data) {
            const currentActive = data.requests.active;
            // 优先使用智能容量管理器的值，否则使用instances.total
            const totalCapacity = data.performance?.capacity?.limits?.contextPoolSize || data.instances.total;
            
            // 更新峰值并发
            peakConcurrent = Math.max(peakConcurrent, currentActive);
            
            // 记录负载历史
            loadHistory.push(currentActive);
            if (loadHistory.length > 60) {
                loadHistory.shift();
            }
            
            // 计算平均负载
            const avgLoad = loadHistory.length > 0 ? 
                loadHistory.reduce((sum, val) => sum + val, 0) / loadHistory.length : 0;
            
            // 更新汇总指标
            document.getElementById('currentActive').textContent = currentActive;
            document.getElementById('peakConcurrent').textContent = peakConcurrent;
            document.getElementById('avgLoad').textContent = avgLoad.toFixed(1);
            document.getElementById('totalCapacity').textContent = totalCapacity;
        }
        
        // 更新图表
        function updateCharts(data) {
            if (loadChart) {
                const currentActive = data.requests.active;
                const now = new Date();
                loadChart.addData(currentActive, now);
            }
        }
        
        // 页面加载完成后启动监控
        document.addEventListener('DOMContentLoaded', () => {
            // 延迟初始化图表，确保DOM完全渲染
            setTimeout(() => {
                initCharts();
                startMonitoring();
            }, 100);
        });

        // 窗口大小调整时重新调整图表
        window.addEventListener('resize', () => {
            if (loadChart) {
                loadChart.resize();
            }
        });

        // 页面离开时停止监控
        window.addEventListener('beforeunload', stopMonitoring);
        
        // 页面可见性变化时控制监控
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                stopMonitoring();
            } else {
                startMonitoring();
            }
        });
        
        // 绘制双折线图表函数
        function drawDualLineChart(canvasId, data1, data2, color1, color2) {
            const canvas = document.getElementById(canvasId);
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            
            if (data1.length < 2 || data2.length < 2) return;
            
            // 设置画布大小适应容器 - 支持高DPI
            const rect = canvas.getBoundingClientRect();
            const dpr = window.devicePixelRatio || 1;
            
            canvas.width = rect.width * dpr;
            canvas.height = rect.height * dpr;
            canvas.style.width = rect.width + 'px';
            canvas.style.height = rect.height + 'px';
            
            ctx.scale(dpr, dpr);
            
            const w = rect.width;
            const h = rect.height;
            
            // 为左右刻度留出紧凑空间
            const leftPadding = 25;   // 左侧活跃请求刻度
            const rightPadding = 35;  // 右侧成功率刻度
            const topPadding = 25;    // 上方统计信息
            const bottomPadding = 15; // 底部边距
            const chartWidth = w - leftPadding - rightPadding;
            const chartHeight = h - topPadding - bottomPadding;
            
            // 获取数据范围
            const maxData1 = Math.max(...data1, 1);
            const maxData2 = 100; // 成功率最大100%
            
            // 绘制第一条线（活跃请求）
            if (data1.length >= 2) {
                ctx.beginPath();
                
                // 绘制数据点和线条
                for (let i = 0; i < data1.length; i++) {
                    const x = leftPadding + (i / (data1.length - 1)) * chartWidth;
                    const y = topPadding + (1 - (data1[i] / maxData1)) * chartHeight;
                    
                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        // 使用贝塞尔曲线创建平滑线条
                        const prevX = leftPadding + ((i - 1) / (data1.length - 1)) * chartWidth;
                        const prevY = topPadding + (1 - (data1[i - 1] / maxData1)) * chartHeight;
                        
                        const cpx = prevX + (x - prevX) * 0.5;
                        const cpy = prevY;
                        const cpx2 = prevX + (x - prevX) * 0.5;
                        const cpy2 = y;
                        
                        ctx.bezierCurveTo(cpx, cpy, cpx2, cpy2, x, y);
                    }
                }
                
                // 绘制填充区域（活跃请求）
                const fillPath1 = new Path2D();
                fillPath1.moveTo(leftPadding, h - bottomPadding); // 底部左边
                
                for (let i = 0; i < data1.length; i++) {
                    const x = leftPadding + (i / (data1.length - 1)) * chartWidth;
                    const y = topPadding + (1 - (data1[i] / maxData1)) * chartHeight;
                    
                    if (i === 0) {
                        fillPath1.lineTo(x, y);
                    } else {
                        const prevX = leftPadding + ((i - 1) / (data1.length - 1)) * chartWidth;
                        const prevY = topPadding + (1 - (data1[i - 1] / maxData1)) * chartHeight;
                        
                        const cpx = prevX + (x - prevX) * 0.5;
                        const cpy = prevY;
                        const cpx2 = prevX + (x - prevX) * 0.5;
                        const cpy2 = y;
                        
                        fillPath1.bezierCurveTo(cpx, cpy, cpx2, cpy2, x, y);
                    }
                }
                
                fillPath1.lineTo(leftPadding + chartWidth, h - bottomPadding); // 底部右边
                fillPath1.closePath();
                
                // 填充区域
                const gradient1 = ctx.createLinearGradient(0, topPadding, 0, h - bottomPadding);
                gradient1.addColorStop(0, color1 + '30'); // 30% 透明度（降低透明度避免遮挡文字）
                gradient1.addColorStop(1, color1 + '05'); // 5% 透明度
                ctx.fillStyle = gradient1;
                ctx.fill(fillPath1);
                
                // 绘制线条
                ctx.strokeStyle = color1;
                ctx.lineWidth = 3;
                ctx.lineCap = 'round';
                ctx.lineJoin = 'round';
                ctx.stroke();
            }
            
            // 绘制第二条线（成功率）
            if (data2.length >= 2) {
                ctx.beginPath();
                
                // 绘制数据点和线条
                for (let i = 0; i < data2.length; i++) {
                    const x = leftPadding + (i / (data2.length - 1)) * chartWidth;
                    const y = topPadding + (1 - (data2[i] / maxData2)) * chartHeight;
                    
                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        // 使用贝塞尔曲线创建平滑线条
                        const prevX = leftPadding + ((i - 1) / (data2.length - 1)) * chartWidth;
                        const prevY = topPadding + (1 - (data2[i - 1] / maxData2)) * chartHeight;
                        
                        const cpx = prevX + (x - prevX) * 0.5;
                        const cpy = prevY;
                        const cpx2 = prevX + (x - prevX) * 0.5;
                        const cpy2 = y;
                        
                        ctx.bezierCurveTo(cpx, cpy, cpx2, cpy2, x, y);
                    }
                }
                
                // 绘制线条（不填充，避免与第一条线冲突）
                ctx.strokeStyle = color2;
                ctx.lineWidth = 3;
                ctx.lineCap = 'round';
                ctx.lineJoin = 'round';
                ctx.stroke();
            }
        }
    </script>
</body>
</html>